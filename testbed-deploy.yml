---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        testbed-deploy.yml                                                #
# Version:                                                                        #
#               2025-01-22 Initial                                                #
# Create Date:  2025-01-22                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               Initial commit for testbed-deploy.yml                             #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
include:
  - /gitlab/ansible-collection.yml

variables:
  GIT_STRATEGY: clone
  ENABLE_SAST_SCANS: "false"
  GALAXY_VERSION: "latest"
  BUILDER_LMI_IP: **************
  BUILDER_MGMT_IP: **************
  BUILDER_USER: galaxy
  STORAGE_BACKEND: nexus
  RESOURCE_GROUP: "NULL_GROUP"
  GALAXY_FOLDER: /data/${CLUSTER_NAME}-bundle-cicd-install
  HTTP_PORT: 8090
  VERBOSITY: ""
  ADDITIONAL_FILES: "$CI_PROJECT_DIR/host_vars,$CI_PROJECT_DIR/group_vars,$CI_PROJECT_DIR/files,$CI_PROJECT_DIR/inventory.yml"
  OPENSHIFT_VARS: "$CI_PROJECT_DIR/group_vars/openshift.yml"

generate_galaxy_bundle:
  image: harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:$GALAXY_VERSION
  stage: build
  timeout: 12h
  variables:
    ANSIBLE_FORCE_COLOR: "true"
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: always
    - when: manual
      allow_failure: true
  before_script:
    - echo "Setting up registry authentication and network configuration"
    # Backup original /etc/hosts before any modifications
    - cp /etc/hosts /etc/hosts.backup
    # Ensure Harbor registry resolves correctly (not to localhost)
    - echo "Checking Harbor registry DNS resolution"
    - nslookup harbor.global.lmco.com || echo "DNS lookup failed, will use IP"
    # Create Docker config directory and authenticate
    - mkdir -p ~/.docker
    - echo "Authenticating with Harbor registry using skopeo"
    - skopeo login harbor.global.lmco.com --username ${HARBOR_US_USER} --password ${HARBOR_US_USER_CLI_SECRET}
    # Create Docker config.json for additional authentication
    - echo "{\"auths\":{\"harbor.global.lmco.com\":{\"auth\":\"$(echo -n ${HARBOR_US_USER}:${HARBOR_US_USER_CLI_SECRET} | base64 -w 0)\"}}}" > ~/.docker/config.json
    # Test registry connectivity before proceeding
    - echo "Testing registry connectivity"
    - skopeo inspect docker://harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:${GALAXY_VERSION} --raw || echo "Registry inspection failed"
  script:
    - welcome
    - env
    # Create a custom script to monitor and fix /etc/hosts during execution
    - |
      cat > /tmp/fix_hosts.sh << 'EOF'
      #!/bin/bash
      # Monitor and fix /etc/hosts to prevent Harbor registry redirection
      while true; do
        if grep -q "127.0.0.1.*harbor.global.lmco.com" /etc/hosts; then
          echo "Detected Harbor registry redirected to localhost, fixing..."
          # Remove harbor.global.lmco.com from localhost line
          sed -i 's/harbor\.global\.lmco\.com//g' /etc/hosts
          # Clean up extra spaces
          sed -i 's/  */ /g' /etc/hosts
          echo "Fixed /etc/hosts"
        fi
        sleep 5
      done
      EOF
      chmod +x /tmp/fix_hosts.sh
    # Start the monitoring script in background
    - /tmp/fix_hosts.sh &
    - MONITOR_PID=$!
    # Run the main ansible playbook
    - >
      ansible-playbook -i localhost lmco.bundler.create_bundle.yml
      -e "@$OPENSHIFT_VARS"
      -e "{'cli_galaxy':{'version':'${GALAXY_VERSION}'}}"
      -e "{'bundle_name':'${CLUSTER_NAME}' }"
      -e "{'cli_bundler':{'additional_files':'${ADDITIONAL_FILES}'}}" $VERBOSITY
    # Clean up: stop the monitoring script and restore original hosts file
    - kill $MONITOR_PID 2>/dev/null || true
    - cp /etc/hosts.backup /etc/hosts 2>/dev/null || true
  tags:
    - aws
    - galaxy-xl
    - kubernetes

.deploy-cluster-from-bundle:
  stage: deploy
  image: harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: on_success
  before_script:
    - echo "Copy SSH key to .ssh" &&
      cp $GALAXY_SSH_KEY ~/.ssh/id_rsa &&
      chmod 600 ~/.ssh/id_rsa
    - >
      curl -s --header "PRIVATE-TOKEN: ${GITLAB_TOKEN}" "https://gitlab.global.lmco.com/api/v4/projects/7508/repository/files/scripts%2Fget_bundle.bash/raw?ref=main" > get_bundle.bash
    - >
      curl -s --header "PRIVATE-TOKEN: ${GITLAB_TOKEN}" "https://gitlab.global.lmco.com/api/v4/projects/7508/repository/files/scripts%2Fwait_for_port.sh/raw?ref=main" > wait_for_port.sh
    - scp get_bundle.bash $BUILDER_USER@$BUILDER_LMI_IP:~/get_bundle.sh
    - scp wait_for_port.sh $BUILDER_USER@$BUILDER_LMI_IP:~/wait_for_port.sh
    - ssh $BUILDER_USER@$BUILDER_LMI_IP "
      chmod +x ~/get_bundle.sh &&
      HIVESTAR_USER=${REGISTRY_USER}
      HIVESTAR_PASSWORD=${REGISTRY_PASSWORD}
      ~/get_bundle.sh ${GALAXY_VERSION} ${GALAXY_FOLDER} ${CLUSTER_NAME} ${STORAGE_BACKEND}"
    - scp -rp ~/.ssh/id_rsa $BUILDER_USER@$BUILDER_LMI_IP:${GALAXY_FOLDER}/secrets/id_rsa
    - ssh $BUILDER_USER@$BUILDER_LMI_IP "
      chmod +x ~/wait_for_port.sh &&
      ~/wait_for_port.sh ${HTTP_PORT}"
  script:
    - ssh $BUILDER_USER@$BUILDER_LMI_IP "cd ${GALAXY_FOLDER} && GALAXY_BUILD_PORT=${HTTP_PORT} GALAXY_BUILD_LMI_IP=${BUILDER_LMI_IP} GALAXY_BUILD_MGMT_IP=${BUILDER_MGMT_IP} EXTRA_ANSIBLE_ARGS='< /dev/null' ./openshift bash.sh"
  tags:
    - kraken
  timeout: 8h

.deploy-cluster:
  extends: .deploy-cluster-from-bundle
  resource_group: "$RESOURCE_GROUP"

deploy-cluster:
  extends: .deploy-cluster
  needs:
    - generate_galaxy_bundle

.manual-deploy:
  rules:
    - if: $CI_PIPELINE_SOURCE != "schedule"
      when: manual
  stage: test

deploy-manually:
  extends:
    - .deploy-cluster
    - .manual-deploy
