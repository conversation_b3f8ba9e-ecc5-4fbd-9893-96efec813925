---
- name: Submit CSR to Venafi TPP API
  hosts: localhost
  gather_facts: false
  vars:
    csr_file: "{{ csr_file | default('') }}"
    primary_dns: "{{ primary_dns | default('') }}"
    san_dns: "{{ san_dns | default('') }}"
    
  tasks:
    - name: Read CSR file content
      slurp:
        src: "{{ csr_file }}"
      register: csr_content
      when: csr_file != ''
    
    - name: Create Venafi certificate request body
      set_fact:
        venafi_request_body:
          PolicyDN: "{{ venafi_policy_dn | default('\\VED\\Policy\\Certificates\\HSBC\\ITID') }}"
          SubjectAltNames:
            - "{{ primary_dns }}"
            - "{{ san_dns }}"
          Subject:
            CommonName: "{{ primary_dns }}"
            Organization: "HSBC"
            OrganizationalUnit: "ITID"
            Country: "GB"
          CSR: "{{ csr_content.content | b64decode }}"
          ValidityPeriod: "{{ certificate_validity | default('P1Y') }}"
      when: csr_content.content is defined
    
    - name: Submit certificate request to <PERSON><PERSON><PERSON>
      uri:
        url: "https://{{ tpp_Host }}/vedsdk/certificates/request"
        method: POST
        headers:
          Authorization: "Bearer {{ tpp_Token }}"
          Content-Type: "application/json"
        body: "{{ venafi_request_body | to_json }}"
        body_format: json
        validate_certs: no
        return_content: yes
      register: venafi_response
      when: venafi_request_body is defined
    
    - name: Display Venafi response
      debug:
        var: venafi_response
      when: venafi_response is defined
    
    - name: Check if certificate request was successful
      fail:
        msg: "Failed to submit CSR to Venafi. Response: {{ venafi_response.content }}"
      when: 
        - venafi_response is defined
        - venafi_response.status != 200
    
    - name: Wait for certificate approval (if required)
      uri:
        url: "https://{{ tpp_Host }}/vedsdk/certificates/{{ venafi_response.json.CertificateDN }}"
        method: GET
        headers:
          Authorization: "Bearer {{ tpp_Token }}"
        validate_certs: no
        return_content: yes
      register: cert_status
      when: 
        - venafi_response is defined
        - venafi_response.json.CertificateDN is defined
      retries: 10
      delay: 30
      until: cert_status.json.Status == "Valid" or cert_status.json.Status == "Issued"
    
    - name: Download approved certificate
      uri:
        url: "https://{{ tpp_Host }}/vedsdk/certificates/{{ venafi_response.json.CertificateDN }}/download/format/pem"
        method: GET
        headers:
          Authorization: "Bearer {{ tpp_Token }}"
        validate_certs: no
        dest: "{{ csr_file | replace('.csr', '.crt') }}"
      when: 
        - cert_status is defined
        - cert_status.json.Status in ['Valid', 'Issued']
    
    - name: Display certificate download status
      debug:
        msg: "Certificate downloaded successfully to {{ csr_file | replace('.csr', '.crt') }}"
      when: 
        - cert_status is defined
        - cert_status.json.Status in ['Valid', 'Issued']

