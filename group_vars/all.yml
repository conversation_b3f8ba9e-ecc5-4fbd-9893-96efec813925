---
# Global variables for all groups
# These variables apply to all hosts in the inventory

# Project Information
project_name: andromeda
environment: testbed
deployment_date: "{{ ansible_date_time.iso8601 }}"

# Common Paths
base_path: "/opt/galaxy"
logs_path: "/var/log/galaxy"
config_path: "/etc/galaxy"

# Common Network Settings
dns_servers:
  - "*******"
  - "*******"

# Security Settings
enable_firewall: true
enable_selinux: true

# Monitoring
enable_monitoring: true
monitoring_port: 9090

# Backup Configuration
backup_enabled: true
backup_retention_days: 30
