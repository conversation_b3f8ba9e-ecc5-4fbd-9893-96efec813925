---
# OpenShift Configuration for Andromeda Testbed
# This file contains OpenShift-specific variables for the testbed deployment

# Cluster Configuration
cluster:
  name: andromeda
  domain: example.com  # Replace with your actual domain
  version: "4.14.31"

# Network Configuration
networking:
  cluster_network:
    - cidr: "**********/14"
      host_prefix: 23
  service_network:
    - "**********/16"
  machine_network:
    - cidr: "***********/24"  # Replace with your actual network

# Platform Configuration
platform:
  type: aws  # or vsphere, baremetal, etc.
  region: us-gov-east-1  # Replace with your region

# Pull Secret Configuration
pull_secret_file: "pull-secret.json"

# Registry Configuration
registry:
  mirror: false
  insecure_registries: []

# Bundler Configuration - Import from bundler.yml
# The bundler configuration is now in group_vars/bundler.yml
# This prevents conflicts during bundle creation

# AWS Specific Configuration (if using AWS)
aws:
  region: us-gov-east-1
  access_key_id: "{{ lookup('env', 'AWS_ACCESS_KEY_ID') }}"
  secret_access_key: "{{ lookup('env', 'AWS_SECRET_ACCESS_KEY') }}"

# Builder Configuration
builder:
  lmi_ip: "{{ lookup('env', 'BUILDER_LMI_IP') | default('*************') }}"
  mgmt_ip: "{{ lookup('env', 'BUILDER_MGMT_IP') | default('*************') }}"
  user: galaxy
