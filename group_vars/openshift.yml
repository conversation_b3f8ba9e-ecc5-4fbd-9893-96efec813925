---
# OpenShift Configuration for Andromeda Testbed
# This file contains OpenShift-specific variables for the testbed deployment

# Cluster Configuration
cluster:
  name: andromeda
  domain: example.com  # Replace with your actual domain
  version: "4.14.31"

# Network Configuration
networking:
  cluster_network:
    - cidr: "**********/14"
      host_prefix: 23
  service_network:
    - "**********/16"
  machine_network:
    - cidr: "***********/24"  # Replace with your actual network

# Platform Configuration
platform:
  type: aws  # or vsphere, baremetal, etc.
  region: us-gov-east-1  # Replace with your region

# Pull Secret Configuration
pull_secret_file: "pull-secret.json"

# Registry Configuration
registry:
  mirror: false
  insecure_registries: []

# Bundler Configuration
bundler:
  nexus_storage: "nexus-storage.tar"
  version_vars_file: "version_vars.yml"
  openshift_install_file: "openshift-install"
  openshift_version: "4.14.31"
  redhat_registry_pull_secret_file: "pull-secret.json"
  rhel_iso_name: "rhel-9.4-x86_64-dvd.iso"

# AWS Specific Configuration (if using AWS)
aws:
  region: us-gov-east-1
  access_key_id: "{{ lookup('env', 'AWS_ACCESS_KEY_ID') }}"
  secret_access_key: "{{ lookup('env', 'AWS_SECRET_ACCESS_KEY') }}"

# Builder Configuration
builder:
  lmi_ip: "{{ lookup('env', 'BUILDER_LMI_IP') | default('*************') }}"
  mgmt_ip: "{{ lookup('env', 'BUILDER_MGMT_IP') | default('*************') }}"
  user: galaxy
