---
# ITID SSL Certificate Renewal Variables

# ITID Configuration
cluster_name_id: "itid"
itid_type: "cloudera"

# DNS Configuration for ITID
# Primary DNS: server.hc.cloud.uk.hsbc (actual server DNS)
# SAN DNS: server.systems.uk.hsbc (CM registered DNS)
primary_dns_suffix: "hc.cloud.uk.hsbc"
san_dns_suffix: "systems.uk.hsbc"

# Venafi TPP Configuration
tpp_Host: "{{ venafi_tpp_host | default('venafi-tpp.systems.uk.hsbc') }}"
tpp_Username: "{{ venafi_username | default('') }}"
tpp_Password: "{{ venafi_password | default('') }}"

# Certificate Policy and Settings
venafi_policy_dn: "\\VED\\Policy\\Certificates\\HSBC\\ITID"
certificate_validity: "P1Y"  # 1 year validity
certificate_key_size: 2048

# Build and Output Directories
build_base_dir: "/gbds/dfp_renewals"
tls_working_dir: "{{ build_base_dir }}/build/tls"
certificate_output_dir: "{{ tls_working_dir }}/output"
central_cert_location: "/gbdspkg/Tooling/Venafi/STD"

# Script Execution Settings
script_timeout: 300  # 5 minutes timeout for scripts
retry_attempts: 3
retry_delay: 30

# Certificate Subject Information
cert_organization: "HSBC"
cert_organizational_unit: "ITID"
cert_country: "GB"
cert_state: "England"
cert_locality: "London"

# Security Settings
file_permissions: "0644"
directory_permissions: "0755"
certificate_permissions: "0600"
key_permissions: "0400"

# Logging and Debug
enable_debug: true
log_level: "info"
log_file: "/var/log/itid_ssl_renewal.log"

# Backup Settings
enable_backup: true
backup_directory: "/gbds/dfp_renewals/backups"
backup_retention_days: 30
