---
# Bundler-specific configuration for Andromeda testbed
# This file contains bundler variables that override defaults

# Registry Configuration
bundler:
  # OpenShift cluster name
  openshift_cluster: andromeda
  
  # Platform configuration
  platform: aws
  distro: standard
  
  # OpenShift version
  openshift_version: "4.14.31"
  
  # Registry URLs - these should NOT include harbor.global.lmco.com
  # during bundle creation to avoid DNS conflicts
  default_registry_url: "galaxy-registry.local"
  default_nexus_url: "galaxy-nexus.local"
  
  # Nexus configuration
  nexus_domain: "galaxy-nexus.apps.galaxy-uge1-ocp02.us.lmco.com"
  nexus_repo_path: "repository/galaxy-static"
  nexus_http_port: 8081
  registry_port: 8082
  
  # Authentication
  galaxy_nexus_user: "admin"
  galaxy_nexus_password: "{{ lookup('env', 'NEXUS_PASSWORD') | default('admin123') }}"
  nexus_user: "{{ lookup('env', 'NEXUS_USER') | default('galaxy-hivestar') }}"
  nexus_password: "{{ lookup('env', 'NEXUS_PASSWORD') }}"
  
  # File configurations
  verson_vars_file: "version_vars.yml"
  openshift_install_file: "openshift-install"
  nexus_storage: "nexus-storage.tar"
  nexus_image_file: "nexus-image.tar"
  rhel_iso_name: "rhel-9.4-x86_64-dvd.iso"
  rhel_major_version: 9
  rhel_minor_version: 4
  
  # Pull secret configuration
  redhat_registry_pull_secret_file: "{{ ansible_env.PWD }}/files/pull-secret.json"
  
  # Proxy settings
  http_proxy: ""
  standard_no_proxy_addresses:
    - "localhost"
    - "127.0.0.1"
    - ".local"
    - ".svc"
    - ".cluster.local"
  
  # DNF ISOs (empty for AWS deployment)
  dnf_isos: []

# Override proxying_mirror to false during bundle creation
# to prevent external registries from being redirected to localhost
proxying_mirror: false

# Ensure domain_mappings doesn't include harbor.global.lmco.com
domain_mappings: []

# Override no_proxy to ensure Harbor registry is accessible
no_proxy: "localhost,127.0.0.1,.local,.svc,.cluster.local"

# Galaxy image configuration
galaxy_image: "harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:{{ lookup('env', 'GALAXY_VERSION') | default('latest') }}"

# Package files location
package_files: "/opt/package_files"
package_files_galaxy_folder: "/opt/package_files/galaxy_files"
