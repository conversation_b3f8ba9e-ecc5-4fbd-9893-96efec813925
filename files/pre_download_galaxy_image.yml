---
# Pre-download Galaxy image before /etc/hosts gets modified
# This task runs before setup_container.yml to avoid DNS conflicts

- name: pre_download | Ensure package files directory exists
  ansible.builtin.file:
    path: "{{ package_files | default('/opt/package_files') }}"
    state: directory
    mode: "0755"

- name: pre_download | Download Galaxy image before DNS redirection
  ansible.builtin.shell:
    cmd: |
      echo "Downloading Galaxy image from external registry..."
      skopeo copy docker://{{ galaxy_image_external }} docker-archive:{{ package_files | default('/opt/package_files') }}/lmco.galaxy.tar
    creates: "{{ package_files | default('/opt/package_files') }}/lmco.galaxy.tar"
  vars:
    galaxy_image_external: "harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:{{ lookup('env', 'GALAXY_VERSION') | default('latest') }}"
  environment:
    # Ensure we use external DNS resolution
    no_proxy: ""
    NO_PROXY: ""

- name: pre_download | Verify Galaxy image was downloaded
  ansible.builtin.stat:
    path: "{{ package_files | default('/opt/package_files') }}/lmco.galaxy.tar"
  register: galaxy_image_stat

- name: pre_download | Fail if Galaxy image download failed
  ansible.builtin.fail:
    msg: "Failed to download Galaxy image from Harbor registry"
  when: not galaxy_image_stat.stat.exists
