---
# Override for download_bundle_files.yml to skip Galaxy image download if already exists
# This prevents the DNS conflict issue

- name: download_bundle_files | Check if Galaxy image already exists
  ansible.builtin.stat:
    path: "{{ package_files }}/lmco.galaxy.tar"
  register: galaxy_image_exists

- name: download_bundle_files | Download lmco.galaxy image as lmco.galaxy.tar image name - {{ galaxy_image }}
  ansible.builtin.shell:
    cmd: |
      echo "Galaxy image already downloaded, skipping..."
      ls -la {{ package_files }}/lmco.galaxy.tar
  when: galaxy_image_exists.stat.exists
  changed_when: false

- name: download_bundle_files | Download lmco.galaxy image as lmco.galaxy.tar image name - {{ galaxy_image }}
  ansible.builtin.shell:
    cmd: |
      skopeo copy docker://{{ galaxy_image }} docker-archive:{{ package_files }}/lmco.galaxy.tar
  when: not galaxy_image_exists.stat.exists
  changed_when: true

- name: download_bundle_files | Download static files
  when: not bundler.distro == "sno"
  ansible.builtin.get_url:
    url: "https://{{ bundler.nexus_domain }}/{{ bundler.nexus_repo_path }}/{{ item }}"
    username: "{{ bundler.nexus_user }}"
    password: "{{ bundler.nexus_password }}"
    dest: "{{ package_files_galaxy_folder }}/{{ item }}"
    mode: "0644"
    timeout: 600
  loop:
    - "{{ bundler.ocp_rhel_iso_name if bundler.platform == 'baremetal' else [] }}"
    - "{{ bundler.nexus_image_file }}"

- name: download_bundle_files | Copy over additional files
  ansible.builtin.copy:
    src: "{{ additional_file }}"
    dest: "{{ package_files }}/"
    mode: "0755"
  loop: "{{ additional_files }}"
  loop_control:
    loop_var: additional_file
  vars:
    additional_files: "{{ bundler.additional_files.split(',') if bundler.additional_files else [] }}"

- name: download_bundle_files | Copy over run script
  ansible.builtin.template:
    src: big-bang.sh.j2
    dest: "{{ package_files }}/big-bang.sh"
    mode: "0755"

- name: download_bundle_files | Create secrets folder
  ansible.builtin.file:
    path: "{{ package_files }}/secrets"
    state: directory
    mode: "0755"
