---
- name: ITID Cluster Certificate Deployment
  hosts: localhost
  gather_facts: false
  vars:
    cluster_name_id: "{{ cluster_name_id | default('itid') }}"
    primary_dns: "{{ primary_dns | default(cluster_name_id + '.hc.cloud.uk.hsbc') }}"
    san_dns: "{{ san_dns | default(cluster_name_id + '.systems.uk.hsbc') }}"
    certificate_source: "{{ certificate_source | default('/gbdspkg/Tooling/Venafi/STD') }}"
    
  tasks:
    - name: Display ITID cluster deployment information
      debug:
        msg: |
          Deploying certificates for ITID cluster:
          - Cluster ID: {{ cluster_name_id }}
          - Primary DNS: {{ primary_dns }}
          - SAN DNS: {{ san_dns }}
          - Certificate Source: {{ certificate_source }}
    
    - name: Find certificates for this cluster
      find:
        paths: "{{ certificate_source }}"
        patterns: "*{{ cluster_name_id }}*"
        file_type: file
      register: cluster_certificates
    
    - name: Fail if no certificates found for cluster
      fail:
        msg: "No certificates found for cluster {{ cluster_name_id }} in {{ certificate_source }}"
      when: cluster_certificates.files | length == 0
    
    - name: Display found certificates
      debug:
        msg: "Found certificates: {{ cluster_certificates.files | map(attribute='name') | list }}"
    
    - name: Create certificate deployment directory on target servers
      file:
        path: "/etc/ssl/certs/{{ cluster_name_id }}"
        state: directory
        mode: '0755'
        owner: "{{ cert_owner | default('root') }}"
        group: "{{ cert_group | default('root') }}"
      delegate_to: "{{ item }}"
              with_items: "{{ groups['itid_hosts'] | default([]) }}"
        when: groups['itid_hosts'] is defined and groups['itid_hosts'] | length > 0
    
    - name: Copy certificates to target servers
      copy:
        src: "{{ item.path }}"
        dest: "/etc/ssl/certs/{{ cluster_name_id }}/"
        mode: '0644'
        owner: "{{ cert_owner | default('root') }}"
        group: "{{ cert_group | default('root') }}"
      delegate_to: "{{ target_host | default('localhost') }}"
      with_items: "{{ cluster_certificates.files }}"
      when: target_host is defined
    
    - name: Update certificate configuration files
      template:
        src: "{{ item.template | default('cert_config.conf.j2') }}"
        dest: "{{ item.dest }}"
        mode: '0644'
        owner: "{{ cert_owner | default('root') }}"
        group: "{{ cert_group | default('root') }}"
        backup: yes
      delegate_to: "{{ target_host | default('localhost') }}"
      with_items: "{{ cert_config_files | default([]) }}"
      when: 
        - target_host is defined
        - cert_config_files is defined
        - cert_config_files | length > 0
    
    - name: Verify certificate deployment
      stat:
        path: "/etc/ssl/certs/{{ cluster_name_id }}/"
      register: cert_deployment_check
      delegate_to: "{{ target_host | default('localhost') }}"
      when: target_host is defined
    
    - name: Display deployment status
      debug:
        msg: |
          Certificate deployment for {{ cluster_name_id }}:
          - Status: {{ 'SUCCESS' if cert_deployment_check.stat.exists else 'FAILED' }}
          - Location: /etc/ssl/certs/{{ cluster_name_id }}/
          - Certificates: {{ cluster_certificates.files | length }}
