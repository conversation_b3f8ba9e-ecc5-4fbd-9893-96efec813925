# ITID Certificate Configuration
# Generated on {{ ansible_date_time.iso8601 }}

[{{ cluster_name_id }}]
# Primary DNS configuration
primary_dns = {{ primary_dns }}
san_dns = {{ san_dns }}

# Certificate paths
cert_path = /etc/ssl/certs/{{ cluster_name_id }}/{{ cluster_name_id }}.crt
key_path = /etc/ssl/certs/{{ cluster_name_id }}/{{ cluster_name_id }}.key
ca_path = /etc/ssl/certs/{{ cluster_name_id }}/ca.crt

# Certificate settings
cert_validity = {{ certificate_validity | default('P1Y') }}
key_size = {{ certificate_key_size | default(2048) }}

# Service configuration
service_name = {{ itid_service_name | default('itid-service') }}
restart_required = true

# Security settings
file_permissions = {{ file_permissions | default('0644') }}
directory_permissions = {{ directory_permissions | default('0755') }}

# Backup settings
backup_enabled = {{ enable_backup | default(true) }}
backup_retention_days = {{ backup_retention_days | default(30) }}

# Logging
log_level = {{ log_level | default('info') }}
log_file = {{ log_file | default('/var/log/itid_ssl_renewal.log') }}
