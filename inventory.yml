---
# Ansible Inventory for Andromeda Testbed
all:
  hosts:
    localhost:
      ansible_connection: local
      ansible_python_interpreter: "{{ ansible_playbook_python }}"
  
  children:
    testbed:
      hosts:
        andromeda:
          cluster_name: andromeda
          ansible_host: localhost
          ansible_connection: local
    
    builders:
      hosts:
        builder:
          ansible_host: "{{ builder.lmi_ip | default('*************') }}"
          ansible_user: galaxy
          ansible_ssh_private_key_file: ~/.ssh/id_rsa
  
  vars:
    # Global variables for all hosts
    cluster_name: andromeda
    deployment_type: aws
    openshift_version: "4.14.31"
