---
# Host-specific variables for localhost
# This file contains variables specific to the localhost where the bundler runs

# Ansible Configuration
ansible_connection: local
ansible_python_interpreter: "{{ ansible_playbook_python }}"

# Local Paths
local_workspace: "/tmp/andromeda-workspace"
bundle_output_path: "/opt/package_files"

# Container Configuration
container_runtime: podman

# Temporary directories
temp_dirs:
  - "/tmp/andromeda-build"
  - "/tmp/andromeda-assets"

# Local registry configuration
local_registry:
  enabled: false
  port: 5000
